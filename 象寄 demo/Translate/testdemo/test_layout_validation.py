#!/usr/bin/env python3
"""
智能图像翻译测试程序
使用OCR识别文字位置，然后让Gemini分析对齐分组和翻译需求，最后进行精确擦除和重绘
"""

import os
import sys
import json
import base64
import requests
from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from processors.layout_processor import LayoutProcessor
from processors.ocr_processor import OCRProcessor
from config.settings import ConfigManager

class SmartTranslationTester:
    """智能图像翻译测试器"""
    
    def __init__(self):
        self.api_key = "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"
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.5-pro-preview"
        
        # 初始化处理器
        self.config_manager = ConfigManager()
        self.ocr_processor = OCRProcessor()
        self.layout_processor = LayoutProcessor()
        
        # 设置输出目录
        self.output_dir = "testdemo/smart_translation_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"智能图像翻译测试器初始化完成")
        print(f"使用模型: {self.model}")
        print(f"输出目录: {self.output_dir}")
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    
    def extract_text_regions(self, image_path: str) -> Tuple[List, List]:
        """提取所有文本区域（中文和其他语言）"""
        print(f"\n开始OCR识别: {image_path}")
        
        # OCR识别
        ocr_result = self.ocr_processor.process_image(image_path)
        if not ocr_result.success:
            raise Exception(f"OCR处理失败: {ocr_result.error_message}")
        
        chinese_regions = ocr_result.data.chinese_regions
        other_regions = ocr_result.data.other_regions
        
        print(f"识别到 {len(chinese_regions)} 个中文区域")
        print(f"识别到 {len(other_regions)} 个其他语言区域")
        
        return chinese_regions, other_regions
    
    def create_gemini_analysis_prompt(self, chinese_regions: List, other_regions: List) -> str:
        """创建Gemini分析提示词 - 新的两阶段分析"""

        # 构建所有文本区域信息
        all_regions = []
        region_id = 0

        # 添加中文区域
        for region in chinese_regions:
            all_regions.append({
                "id": region_id,
                "text": region.text,
                "language": "chinese",
                "bbox": region.bbox,
                "left": region.bbox[0],
                "top": region.bbox[1],
                "right": region.bbox[0] + region.bbox[2],
                "bottom": region.bbox[1] + region.bbox[3],
                "center_x": region.bbox[0] + region.bbox[2]//2,
                "center_y": region.bbox[1] + region.bbox[3]//2,
                "width": region.bbox[2],
                "height": region.bbox[3]
            })
            region_id += 1

        # 添加其他语言区域
        for region in other_regions:
            all_regions.append({
                "id": region_id,
                "text": region.text,
                "language": "other",
                "bbox": region.bbox,
                "left": region.bbox[0],
                "top": region.bbox[1],
                "right": region.bbox[0] + region.bbox[2],
                "bottom": region.bbox[1] + region.bbox[3],
                "center_x": region.bbox[0] + region.bbox[2]//2,
                "center_y": region.bbox[1] + region.bbox[3]//2,
                "width": region.bbox[2],
                "height": region.bbox[3]
            })
            region_id += 1

        regions_data_str = json.dumps(all_regions, ensure_ascii=False, indent=2)

        prompt = f"""
请分析这张图片中的文字布局和翻译需求。我需要你完成两个独立的任务：

OCR识别的文字区域信息：
```json
{regions_data_str}
```

## 任务1：对齐和分组分析
分析所有文字区域的空间关系和对齐模式，用于后续程序渲染验证。

## 任务2：智能文字识别和翻译分析
1. 重新识别图片中的文字内容（结合OCR结果进行验证和补充）
2. 识别产品主体，剔除产品主体上的装饰性文字（这些不需要翻译）
3. 只处理需要翻译的中文文字
4. 分析每个需要翻译文字的字号、字体、颜色等样式
5. 将中文翻译成日文
6. 根据OCR提供的位置信息给出最终的翻译结果

请返回JSON格式：
```json
{{
    "layout_analysis": {{
        "layout_mode": "simple|horizontal|vertical|grid|complex",
        "main_alignment": "left|center|right|mixed",
        "alignment_groups": {{
            "left_groups": [["文字1", "文字2"], ["文字3"]],
            "center_groups": [["文字4", "文字5"]],
            "right_groups": [["文字6"]],
            "distribution_groups": [["文字7", "文字8"]]
        }},
        "spatial_relationships": "描述文字间的空间关系"
    }},
    "translation_analysis": {{
        "text_recognition": {{
            "verified_texts": [
                {{
                    "region_id": 0,
                    "ocr_text": "深润保湿",
                    "corrected_text": "深润保湿",
                    "confidence": 0.95
                }}
            ],
            "additional_texts": [
                {{
                    "estimated_bbox": [100, 200, 80, 30],
                    "text": "新发现的文字",
                    "confidence": 0.85
                }}
            ]
        }},
        "product_analysis": {{
            "product_main_body": "识别的产品主体描述",
            "decorative_texts": ["装饰文字1", "装饰文字2"]
        }},
        "translation_results": [
            {{
                "region_id": 0,
                "original_text": "深润保湿",
                "japanese_text": "ディープ保湿",
                "font_size": 55,
                "color": [78, 61, 53],
                "font_weight": "bold|normal",
                "font_family": "estimated_font_family",
                "text_align": "left|center|right",
                "bbox": [100, 200, 80, 30],
                "should_erase": true,
                "reason": "产品功效描述文字，需要翻译"
            }}
        ],
        "no_translation": [
            {{
                "region_id": 1,
                "original_text": "某品牌LOGO",
                "reason": "产品主体装饰文字，不需要翻译"
            }}
        ]
    }},
    "confidence": 0.95,
    "summary": "分析完成，识别X个需要翻译的文字，Y个不需要翻译"
}}
```

注意：
- 只翻译中文，其他语言不翻译
- 产品主体上的装饰性文字不要翻译
- 字号、颜色等样式要尽量准确
- should_erase字段决定是否需要用LaMa擦除该区域

只返回JSON，不要其他文字。
"""

        return prompt
    
    def analyze_with_gemini(self, image_path: str, chinese_regions: List, other_regions: List) -> Dict[str, Any]:
        """使用Gemini分析布局和翻译需求"""
        print(f"\n🤖 正在请求{self.model}分析图片...")
        
        # 编码图片
        base64_image = self.encode_image_to_base64(image_path)
        
        # 创建提示词
        prompt = self.create_gemini_analysis_prompt(chinese_regions, other_regions)
        
        # 构建请求
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 8000,
            "temperature": 0.1
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            print(f"✅ {self.model}响应成功")
            print(f"响应内容: {content[:200]}...")
            
            # 解析JSON响应
            try:
                content_clean = content.replace('```json', '').replace('```', '').strip()
                json_start = content_clean.find('{')
                if json_start != -1:
                    brace_count = 0
                    json_end = json_start
                    for i, char in enumerate(content_clean[json_start:], json_start):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                json_end = i + 1
                                break
                    
                    if json_end > json_start:
                        json_content = content_clean[json_start:json_end]
                        analysis_result = json.loads(json_content)
                        print(f"✅ JSON解析成功")
                        return analysis_result
                    else:
                        raise ValueError("JSON结构不完整")
                else:
                    raise ValueError("未找到JSON开始标记")
                    
            except Exception as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {content}")
                return {
                    "layout_analysis": {"layout_mode": "unknown", "main_alignment": "unknown", "alignment_groups": {}},
                    "translation_analysis": {"needs_translation": [], "no_translation": []},
                    "confidence": 0.0,
                    "summary": f"解析失败: {e}"
                }
            
        except requests.RequestException as e:
            print(f"❌ API请求失败: {e}")
            return {
                "layout_analysis": {"layout_mode": "unknown", "main_alignment": "unknown", "alignment_groups": {}},
                "translation_analysis": {"needs_translation": [], "no_translation": []},
                "confidence": 0.0,
                "summary": f"API请求失败: {e}"
            }
    
    def execute_smart_translation(self, image_path: str, gemini_analysis: Dict[str, Any],
                                 chinese_regions: List) -> None:
        """执行智能翻译流程 - 新的基于Gemini判断的流程"""
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        translation_results = translation_analysis.get("translation_results", [])

        if not translation_results:
            print("⚠️ Gemini分析结果显示无需翻译的文字")
            return

        print(f"\n🔥 开始智能翻译流程...")
        print(f"Gemini识别需要翻译的文字: {len(translation_results)} 个")

        try:
            # 1. 导入处理器
            from processors.inpaint_processor import InpaintProcessor
            from processors.renderer import Renderer
            from processors.font_processor import FontProcessor
            from models.data_models import TranslationResult, FontMatchResult, StyleInfo, LayoutResult

            # 2. 加载原始图像
            original_image = cv2.imread(image_path)
            if original_image is None:
                print(f"❌ 无法加载图片: {image_path}")
                return

            # 3. 根据Gemini判断筛选需要擦除的区域
            regions_to_erase = []
            translation_items_to_process = []

            for translation_item in translation_results:
                should_erase = translation_item.get("should_erase", True)
                if not should_erase:
                    print(f"⏭️ 跳过擦除: {translation_item.get('original_text', '')} (Gemini判断不需要擦除)")
                    continue

                region_id = translation_item.get("region_id", -1)
                original_text = translation_item.get("original_text", "")

                # 找到对应的OCR区域
                matching_region = None
                for region in chinese_regions:
                    if region.text == original_text or original_text in region.text:
                        matching_region = region
                        break

                if matching_region:
                    regions_to_erase.append(matching_region)
                    translation_items_to_process.append(translation_item)
                    print(f"✅ 找到匹配区域: '{original_text}' -> 需要擦除")
                else:
                    print(f"⚠️ 未找到匹配的OCR区域: '{original_text}'")

            print(f"最终需要擦除的区域: {len(regions_to_erase)} 个")
            
            # 4. 如果没有需要擦除的区域，直接返回
            if not regions_to_erase:
                print("⚠️ 没有需要擦除的区域，跳过后续处理")
                return

            # 5. 初始化处理器
            inpaint_processor = InpaintProcessor()
            renderer = Renderer(weight_adjustment=400)
            font_processor = FontProcessor()

            # 6. 执行LAMA擦除（只擦除Gemini判断需要擦除的区域）
            print(f"🎯 使用LAMA擦除 {len(regions_to_erase)} 个Gemini判断需要擦除的区域...")
            inpaint_result = inpaint_processor.process_inpainting(original_image, regions_to_erase)

            if not inpaint_result.success:
                print(f"❌ LAMA擦除失败: {inpaint_result.error_message}")
                return

            inpainted_image = inpaint_result.data
            print("✅ LAMA擦除完成")

            # 7. 使用Gemini识别的字体信息（不再执行程序的字体匹配）
            print("使用Gemini识别的字体信息...")
            font_matches = self._create_font_matches_from_gemini(translation_items_to_process)

            # 8. 构建翻译对象（使用Gemini的翻译结果）
            print("构建翻译对象...")
            translation_objects = self._build_smart_translation_objects(
                translation_items_to_process, regions_to_erase, font_matches
            )
            
            # 9. 创建布局结果（基于Gemini的布局分析）
            layout_analysis = gemini_analysis.get("layout_analysis", {})
            alignment_groups = layout_analysis.get("alignment_groups", {})

            # 构建兼容的水平对齐结构
            horizontal_alignment = {
                "type": layout_analysis.get("main_alignment", "center"),
                "left_groups": alignment_groups.get("left_groups", []),
                "center_groups": alignment_groups.get("center_groups", []),
                "right_groups": alignment_groups.get("right_groups", []),
                "distribution_groups": alignment_groups.get("distribution_groups", [])
            }

            layout_result = LayoutResult(
                layout_mode=layout_analysis.get("layout_mode", "simple"),
                regions=[],  # 这里传空列表，因为我们使用Gemini的分析结果
                horizontal_alignment=horizontal_alignment,
                vertical_distribution={"type": "simple"},
                alignment_strategies=[]
            )

            # 10. 渲染翻译文字（基于Gemini的翻译结果）
            print(f"🎨 渲染 {len(translation_objects)} 个日文文本...")
            render_result = renderer.render_translations(
                inpainted_image, translation_objects, layout_result, image_path
            )

            if not render_result.success:
                print(f"❌ 文本渲染失败: {render_result.error_message}")
                return

            final_image = render_result.data['image']
            render_log = render_result.data['render_log']

            # 11. 保存结果
            output_path = os.path.join(self.output_dir, "smart_translated.png")
            cv2.imwrite(output_path, final_image)
            print(f"✅ 智能翻译结果已保存: {output_path}")

            # 12. 保存对比图
            self._save_comparison_image(original_image, final_image, "smart_translation_comparison.png")

            # 13. 输出渲染日志
            print(f"\n📝 渲染日志:")
            for i, log in enumerate(render_log, 1):
                print(f"  {i}. '{log['original']}' → '{log['translated']}' "
                      f"at ({log['position'][0]}, {log['position'][1]}) "
                      f"size {log['size']}px")

            print(f"\n🎉 智能翻译流程完成！处理了 {len(render_log)} 个文本")

            # 14. 输出Gemini分析摘要
            print(f"\n📊 Gemini分析摘要:")
            translation_analysis = gemini_analysis.get("translation_analysis", {})
            no_translation = translation_analysis.get("no_translation", [])
            print(f"  - 需要翻译: {len(translation_objects)} 个")
            print(f"  - 不需要翻译: {len(no_translation)} 个")
            print(f"  - 实际擦除: {len(regions_to_erase)} 个区域")
            print(f"  - 置信度: {gemini_analysis.get('confidence', 0.0):.2f}")
            
        except Exception as e:
            print(f"❌ 智能翻译流程失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _build_smart_translation_objects(self, translation_items: List[Dict],
                                        regions_to_erase: List, font_matches: List) -> List:
        """构建智能翻译对象 - 基于Gemini的翻译结果"""
        from models.data_models import TranslationResult, StyleInfo

        translation_objects = []

        for translation_item in translation_items:
            original_text = translation_item.get("original_text", "")
            japanese_text = translation_item.get("japanese_text", "")
            font_size = translation_item.get("font_size", 16)
            color = translation_item.get("color", [0, 0, 0])
            font_weight = translation_item.get("font_weight", "normal")
            bbox = translation_item.get("bbox", None)

            # 优先使用Gemini提供的bbox，如果没有则查找OCR区域
            if bbox:
                # 使用Gemini提供的位置信息
                matching_region_bbox = tuple(bbox)
                print(f"✅ 使用Gemini提供的位置: '{original_text}' -> {bbox}")
            else:
                # 查找对应的OCR区域
                matching_region = None
                for region in regions_to_erase:
                    if region.text == original_text or original_text in region.text:
                        matching_region = region
                        break

                if not matching_region:
                    print(f"⚠️ 未找到匹配的OCR区域: {original_text}")
                    continue

                matching_region_bbox = matching_region.bbox

            # 找到对应的字体匹配
            matching_font = None
            for font_match in font_matches:
                if font_match.text == original_text or original_text in font_match.text:
                    matching_font = font_match
                    break

            if not matching_font:
                print(f"⚠️ 未找到匹配的字体: {original_text}，使用默认字体")
                from models.data_models import FontMatchResult
                matching_font = FontMatchResult(
                    text=original_text,
                    matched_font="NotoSansSC",
                    font_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), "fonts/NotoSansSC/NotoSansSC-Black.ttf"),
                    confidence=0.8,
                    supports_japanese=True,
                    region_id=0
                )

            # 构建样式信息（使用Gemini分析的样式）
            style_info = StyleInfo(
                estimated_font_size=font_size,
                precise_height=font_size,
                color=tuple(color) if isinstance(color, list) else color,
                background_color=(255, 255, 255),
                is_bold=(font_weight == "bold"),
                is_dark_text=True,
                contrast_ratio=4.5
            )

            # 创建翻译对象
            translation_obj = TranslationResult(
                original_text=original_text,
                translated_text=japanese_text,
                bbox=matching_region_bbox,
                style_info=style_info,
                font_info=matching_font,
                group_key=f"gemini_group_{len(translation_objects)}",
                group_scale_factor=1.0,
                final_font_size=font_size
            )

            translation_objects.append(translation_obj)
            print(f"✅ 构建翻译对象: '{original_text}' -> '{japanese_text}' (字号:{font_size})")

        return translation_objects

    def _create_font_matches_from_gemini(self, translation_items: List[Dict]) -> List:
        """根据Gemini识别的字体信息创建字体匹配结果"""
        from models.data_models import FontMatchResult

        # 字体映射：Gemini识别的字体名 -> 项目中的字体路径
        font_mapping = {
            "Source Han Sans": "fonts/思源黑体/SourceHanSansSC-Bold.otf",
            "Noto Sans": "fonts/NotoSansSC/NotoSansSC-Black.ttf",
            "Noto Sans SC": "fonts/NotoSansSC/NotoSansSC-Black.ttf",
            "思源黑体": "fonts/思源黑体/SourceHanSansSC-Bold.otf",
            "台北黑体": "fonts/台北黑体/TaipeiSansTCBeta-Bold.ttf"
        }

        default_font_path = "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        font_matches = []

        for i, translation_item in enumerate(translation_items):
            original_text = translation_item.get("original_text", "")
            gemini_font = translation_item.get("font_family", "Source Han Sans")
            font_weight = translation_item.get("font_weight", "normal")

            # 查找对应的字体路径
            font_path = font_mapping.get(gemini_font, default_font_path)

            # 检查字体文件是否存在
            full_font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), font_path)
            if not os.path.exists(full_font_path):
                print(f"⚠️ Gemini识别的字体 '{gemini_font}' 在项目中不存在，使用默认字体")
                font_path = default_font_path
                full_font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), font_path)
                matched_font_name = "NotoSansSC"
            else:
                matched_font_name = gemini_font
                print(f"✅ 使用Gemini识别的字体: '{original_text}' -> {gemini_font}")

            font_match = FontMatchResult(
                text=original_text,
                matched_font=matched_font_name,
                font_path=full_font_path,
                confidence=0.95,  # Gemini识别的置信度较高
                supports_japanese=True,
                region_id=i
            )

            font_matches.append(font_match)

        return font_matches

    def _save_comparison_image(self, original_image: np.ndarray, final_image: np.ndarray,
                              filename: str) -> None:
        """保存对比图"""
        try:
            h, w = original_image.shape[:2]
            comparison = np.zeros((h, w * 2, 3), dtype=np.uint8)
            
            # 左侧：原图
            comparison[:, :w] = original_image
            
            # 右侧：翻译结果
            comparison[:, w:] = final_image
            
            # 添加分割线
            cv2.line(comparison, (w, 0), (w, h), (255, 255, 255), 2)
            
            # 添加标签
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(comparison, "Original", (10, 30), font, 1, (255, 255, 255), 2)
            cv2.putText(comparison, "Smart Translated", (w + 10, 30), font, 1, (255, 255, 255), 2)
            
            # 保存对比图
            comparison_path = os.path.join(self.output_dir, filename)
            cv2.imwrite(comparison_path, comparison)
            print(f"✅ 对比图已保存: {comparison_path}")
            
        except Exception as e:
            print(f"⚠️ 保存对比图失败: {e}")
    
    def generate_layout_debug_image(self, image_path: str, gemini_analysis: Dict[str, Any], 
                                   chinese_regions: List, other_regions: List) -> None:
        """生成布局分析调试图"""
        print(f"\n🎨 生成布局分析调试图...")
        
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        alignment_groups = layout_analysis.get("alignment_groups", {})
        
        # 加载原图
        base_image = cv2.imread(image_path)
        if base_image is None:
            print(f"❌ 无法加载图片: {image_path}")
            return
        
        # 创建区域映射
        all_regions = {}
        for region in chinese_regions + other_regions:
            all_regions[region.text] = {
                'bbox': region.bbox,
                'left': region.bbox[0],
                'top': region.bbox[1],
                'right': region.bbox[0] + region.bbox[2],
                'bottom': region.bbox[1] + region.bbox[3],
                'center': [region.bbox[0] + region.bbox[2]//2, region.bbox[1] + region.bbox[3]//2],
                'text': region.text
            }
        
        # 生成对齐分组调试图
        alignment_types = [
            ('left_groups', '左对齐', (0, 255, 0)),
            ('center_groups', '居中对齐', (255, 0, 0)),
            ('right_groups', '右对齐', (0, 0, 255)),
            ('distribution_groups', '分布对齐', (255, 255, 0))
        ]
        
        for group_type, label, color in alignment_types:
            groups = alignment_groups.get(group_type, [])
            if not groups:
                continue
            
            # 创建图片副本
            img_copy = base_image.copy()
            pil_image = Image.fromarray(cv2.cvtColor(img_copy, cv2.COLOR_BGR2RGB))
            overlay = Image.new('RGBA', pil_image.size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # 尝试加载中文字体
            try:
                font_paths = [
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/STHeiti Light.ttc", 
                    "/System/Library/Fonts/Hiragino Sans GB.ttc"
                ]
                
                font_large = font_medium = font_small = None
                for font_path in font_paths:
                    try:
                        font_large = ImageFont.truetype(font_path, 32)
                        font_medium = ImageFont.truetype(font_path, 24)
                        font_small = ImageFont.truetype(font_path, 18)
                        break
                    except:
                        continue
                
                if not font_large:
                    font_large = font_medium = font_small = ImageFont.load_default()
                    
            except Exception as e:
                font_large = font_medium = font_small = ImageFont.load_default()
            
            # 绘制标题
            title = f"Gemini智能分析 - {label} ({len(groups)}组)"
            draw.text((30, 30), title, fill=(40, 40, 40, 255), font=font_large)
            
            # 绘制各组
            for group_idx, group in enumerate(groups):
                group_color = (*color, 200)
                
                for text in group:
                    if text in all_regions:
                        region = all_regions[text]
                        
                        # 绘制区域框
                        draw.rectangle([
                            region['left'], region['top'], 
                            region['right'], region['bottom']
                        ], outline=group_color, width=2)
                        
                        # 绘制半透明填充
                        draw.rectangle([
                            region['left'], region['top'], 
                            region['right'], region['bottom']
                        ], fill=(*color, 30))
                        
                        # 绘制文本标签
                        label_text = f"{text}"
                        text_x = region['left'] + 2
                        text_y = region['top'] - 25 if region['top'] > 25 else region['bottom'] + 2
                        
                        # 文本背景
                        try:
                            bbox = draw.textbbox((text_x, text_y), label_text, font=font_small)
                            draw.rectangle([bbox[0]-2, bbox[1]-2, bbox[2]+2, bbox[3]+2], 
                                         fill=(255, 255, 255, 220))
                        except:
                            pass
                        
                        draw.text((text_x, text_y), label_text, fill=(40, 40, 40, 255), font=font_small)
            
            # 保存调试图
            final_image = Image.alpha_composite(pil_image.convert('RGBA'), overlay)
            final_image = final_image.convert('RGB')
            cv2_image = cv2.cvtColor(np.array(final_image), cv2.COLOR_RGB2BGR)
            
            filename = f"layout_debug_{group_type}.png"
            output_path = os.path.join(self.output_dir, filename)
            cv2.imwrite(output_path, cv2_image)
            print(f"✅ 布局调试图已保存: {filename}")
    
    def save_analysis_report(self, image_path: str, gemini_analysis: Dict[str, Any], 
                            chinese_regions: List, other_regions: List) -> None:
        """保存分析报告"""
        report = {
            "image_path": image_path,
            "timestamp": datetime.now().isoformat(),
            "ocr_results": {
                "chinese_regions": len(chinese_regions),
                "other_regions": len(other_regions),
                "chinese_texts": [region.text for region in chinese_regions],
                "other_texts": [region.text for region in other_regions]
            },
            "gemini_analysis": gemini_analysis,
            "analysis_summary": {
                "layout_mode": gemini_analysis.get("layout_analysis", {}).get("layout_mode", "unknown"),
                "main_alignment": gemini_analysis.get("layout_analysis", {}).get("main_alignment", "unknown"),
                "needs_translation_count": len(gemini_analysis.get("translation_analysis", {}).get("needs_translation", [])),
                "no_translation_count": len(gemini_analysis.get("translation_analysis", {}).get("no_translation", [])),
                "gemini_confidence": gemini_analysis.get("confidence", 0.0)
            }
        }
        
        report_path = os.path.join(self.output_dir, "smart_analysis_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析报告已保存: {report_path}")
    
    def print_analysis_summary(self, gemini_analysis: Dict[str, Any]) -> None:
        """打印分析结果摘要"""
        print(f"\n📊 Gemini智能分析结果摘要")
        print(f"="*60)
        
        # 布局分析
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        print(f"布局模式: {layout_analysis.get('layout_mode', 'unknown')}")
        print(f"主要对齐: {layout_analysis.get('main_alignment', 'unknown')}")
        
        # 翻译分析 - 新的数据结构
        translation_analysis = gemini_analysis.get("translation_analysis", {})

        # 文字识别结果
        text_recognition = translation_analysis.get("text_recognition", {})
        verified_texts = text_recognition.get("verified_texts", [])
        additional_texts = text_recognition.get("additional_texts", [])

        print(f"文字识别: 验证{len(verified_texts)}个，新发现{len(additional_texts)}个")

        # 产品分析
        product_analysis = translation_analysis.get("product_analysis", {})
        product_main_body = product_analysis.get("product_main_body", "")
        decorative_texts = product_analysis.get("decorative_texts", [])

        if product_main_body:
            print(f"产品主体: {product_main_body}")
        print(f"装饰性文字: {len(decorative_texts)} 个")

        # 翻译结果
        translation_results = translation_analysis.get("translation_results", [])
        no_translation = translation_analysis.get("no_translation", [])

        print(f"需要翻译: {len(translation_results)} 个文字")
        print(f"无需翻译: {len(no_translation)} 个文字")

        if translation_results:
            print(f"\n需要翻译的文字:")
            for i, item in enumerate(translation_results, 1):
                original = item.get("original_text", "")
                japanese = item.get("japanese_text", "")
                font_size = item.get("font_size", "未知")
                color = item.get("color", [0, 0, 0])
                should_erase = item.get("should_erase", True)
                reason = item.get("reason", "")

                color_str = f"RGB({color[0]}, {color[1]}, {color[2]})" if isinstance(color, list) and len(color) >= 3 else str(color)
                erase_status = "需擦除" if should_erase else "不擦除"
                print(f"  {i}. '{original}' → '{japanese}' (字号{font_size}px, 颜色{color_str}, {erase_status})")
                if reason:
                    print(f"     原因: {reason}")

        if no_translation:
            print(f"\n无需翻译的文字:")
            for i, item in enumerate(no_translation, 1):
                original = item.get("original_text", "")
                reason = item.get("reason", "")
                print(f"  {i}. '{original}' - {reason}")
        
        confidence = gemini_analysis.get("confidence", 0.0)
        summary = gemini_analysis.get("summary", "")
        
        print(f"\n置信度: {confidence:.2f}")
        if summary:
            print(f"总结: {summary}")
        
        print(f"="*60)

    def print_gemini_analysis_details(self, gemini_analysis: Dict[str, Any]) -> None:
        """输出详细的Gemini分析结果（人类友好格式）"""
        print(f"\n" + "="*80)
        print(f"🤖 Gemini智能分析详细结果")
        print(f"="*80)

        # 1. 布局分析详情
        layout_analysis = gemini_analysis.get("layout_analysis", {})
        print(f"\n📐 布局分析:")
        print(f"  布局模式: {layout_analysis.get('layout_mode', '未知')}")
        print(f"  主要对齐: {layout_analysis.get('main_alignment', '未知')}")

        spatial_relationships = layout_analysis.get("spatial_relationships", "")
        if spatial_relationships:
            print(f"  空间关系: {spatial_relationships}")

        # 对齐分组详情
        alignment_groups = layout_analysis.get("alignment_groups", {})
        print(f"\n📊 对齐分组详情:")

        for group_type, groups in alignment_groups.items():
            if groups:
                group_type_name = {
                    "left_groups": "左对齐组",
                    "center_groups": "居中对齐组",
                    "right_groups": "右对齐组",
                    "distribution_groups": "分布对齐组"
                }.get(group_type, group_type)

                print(f"  {group_type_name}: {len(groups)} 组")
                for i, group in enumerate(groups, 1):
                    print(f"    组{i}: {group}")

        # 2. 文字识别详情
        translation_analysis = gemini_analysis.get("translation_analysis", {})
        text_recognition = translation_analysis.get("text_recognition", {})

        print(f"\n🔍 文字识别详情:")
        verified_texts = text_recognition.get("verified_texts", [])
        additional_texts = text_recognition.get("additional_texts", [])

        print(f"  验证的OCR文字: {len(verified_texts)} 个")
        for text in verified_texts:
            print(f"    ID{text.get('region_id', '?')}: '{text.get('corrected_text', '')}' (置信度: {text.get('confidence', 0):.2f})")

        if additional_texts:
            print(f"  额外发现的文字: {len(additional_texts)} 个")
            for text in additional_texts:
                bbox = text.get('estimated_bbox', [])
                print(f"    '{text.get('text', '')}' at {bbox} (置信度: {text.get('confidence', 0):.2f})")

        # 3. 产品分析详情
        product_analysis = translation_analysis.get("product_analysis", {})
        print(f"\n🏷️ 产品分析:")
        print(f"  产品主体: {product_analysis.get('product_main_body', '未识别')}")

        decorative_texts = product_analysis.get("decorative_texts", [])
        if decorative_texts:
            print(f"  装饰性文字 ({len(decorative_texts)} 个):")
            for text in decorative_texts:
                print(f"    - '{text}'")

        # 4. 翻译结果详情
        translation_results = translation_analysis.get("translation_results", [])
        print(f"\n🔤 翻译结果详情:")
        print(f"  需要翻译: {len(translation_results)} 个")

        for i, result in enumerate(translation_results, 1):
            original = result.get("original_text", "")
            japanese = result.get("japanese_text", "")
            font_family = result.get("font_family", "未知")
            font_size = result.get("font_size", "未知")
            font_weight = result.get("font_weight", "未知")
            color = result.get("color", [0, 0, 0])
            should_erase = result.get("should_erase", True)
            reason = result.get("reason", "")

            color_str = f"RGB({color[0]}, {color[1]}, {color[2]})" if isinstance(color, list) and len(color) >= 3 else str(color)
            erase_status = "✅需擦除" if should_erase else "❌不擦除"

            print(f"  {i}. '{original}' → '{japanese}'")
            print(f"     字体: {font_family} | 字号: {font_size}px | 字重: {font_weight}")
            print(f"     颜色: {color_str} | {erase_status}")
            print(f"     原因: {reason}")
            print()

        # 5. 不翻译文字详情
        no_translation = translation_analysis.get("no_translation", [])
        if no_translation:
            print(f"🚫 不翻译文字详情:")
            print(f"  不需要翻译: {len(no_translation)} 个")
            for i, item in enumerate(no_translation, 1):
                original = item.get("original_text", "")
                reason = item.get("reason", "")
                print(f"  {i}. '{original}' - {reason}")

        # 6. 总体评估
        confidence = gemini_analysis.get("confidence", 0.0)
        summary = gemini_analysis.get("summary", "")

        print(f"\n📈 总体评估:")
        print(f"  置信度: {confidence:.2f}")
        if summary:
            print(f"  总结: {summary}")

        print(f"="*80)

    def run_smart_translation_test(self, image_path: str) -> None:
        """运行智能翻译测试流程"""
        print(f"\n🚀 开始智能图像翻译测试流程")
        print(f"图片: {image_path}")
        print(f"模型: {self.model}")
        
        try:
            # 1. OCR识别提取文本区域
            chinese_regions, other_regions = self.extract_text_regions(image_path)
            
            # 2. Gemini智能分析
            gemini_analysis = self.analyze_with_gemini(image_path, chinese_regions, other_regions)

            # 2.1 输出人类友好的Gemini分析结果
            self.print_gemini_analysis_details(gemini_analysis)
            
            # 3. 生成布局调试图
            self.generate_layout_debug_image(image_path, gemini_analysis, chinese_regions, other_regions)
            
            # 4. 执行智能翻译
            self.execute_smart_translation(image_path, gemini_analysis, chinese_regions)
            
            # 5. 保存分析报告
            self.save_analysis_report(image_path, gemini_analysis, chinese_regions, other_regions)
            
            # 6. 输出分析摘要
            self.print_analysis_summary(gemini_analysis)
            
        except Exception as e:
            print(f"❌ 智能翻译测试流程失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能图像翻译测试程序")
    parser.add_argument("image", nargs="?", default="../example.jpg", help="测试图片路径")
    
    args = parser.parse_args()
    
    # 检查图片是否存在
    if not os.path.exists(args.image):
        print(f"❌ 图片文件不存在: {args.image}")
        return
    
    # 创建测试器并运行
    tester = SmartTranslationTester()
    tester.run_smart_translation_test(args.image)

if __name__ == "__main__":
    main() 