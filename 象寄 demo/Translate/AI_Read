# AI_Read 文档

## 项目理解记录

### 当前项目状态
- 这是一个图片翻译程序，使用OCR识别文本，然后进行翻译和重绘

### 用户新需求（2025-07-12）
用户要求重新设计流程，使用Gemini进行智能分析：

1. **OCR识别阶段**：获取文本内容和文本框位置
2. **Gemini分析阶段**：
   - 任务1：对齐和分组分析，输出JSON结果用于程序渲染验证
   - 任务2：识别文字、剔除产品主体文字、识别字号字体颜色、输出翻译结果JSON
3. **擦除阶段**：根据Gemini判断的需要翻译区域进行LaMa擦除
4. **重绘阶段**：根据Gemini的翻译结果重绘译文

### 重要注意事项
- 只翻译中文，其他语言不翻译
- LaMa擦除要在Gemini分析之后进行
- Gemini会帮助判断哪些区域不需要擦除（产品主体文字）

### 错误记录
（待补充）

### 解决方案记录
（待补充）
